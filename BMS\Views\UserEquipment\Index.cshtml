@{
    ViewData["Title"] = "用户装备管理";
}

<style>
    @* .modal-backdrop{
       display: none !important;
    } *@

    /* 科幻主题样式 */
    #userEquipmentApp {
        background: transparent !important;
        color: #e2e8f0 !important;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif !important;
        min-height: 100vh !important;
        position: relative !important;
        z-index: 1 !important;
    }

    /* 科幻卡片样式 */
    #userEquipmentApp .cyber-card {
        background: rgba(26, 26, 46, 0.95) !important;
        border: 1px solid rgba(0, 212, 255, 0.3) !important;
        border-radius: 12px !important;
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(10px) !important;
        margin-bottom: 2rem !important;
        position: relative !important;
        overflow: hidden !important;
    }

    #userEquipmentApp .cyber-card::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: -100% !important;
        width: 100% !important;
        height: 2px !important;
        background: linear-gradient(90deg, transparent, #00d4ff, transparent) !important;
        animation: scan 3s infinite !important;
    }

    @@keyframes scan {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    #userEquipmentApp .cyber-card-header {
        background: rgba(0, 212, 255, 0.1) !important;
        border-bottom: 1px solid rgba(0, 212, 255, 0.3) !important;
        padding: 1.5rem !important;
        border-radius: 12px 12px 0 0 !important;
    }

    #userEquipmentApp .cyber-card-title {
        color: #00d4ff !important;
        font-weight: 600 !important;
        font-size: 1.25rem !important;
        margin: 0 !important;
        display: flex !important;
        align-items: center !important;
        gap: 0.75rem !important;
        text-shadow: 0 0 10px rgba(0, 212, 255, 0.5) !important;
    }

    #userEquipmentApp .cyber-card-title i {
        color: #00d4ff !important;
        text-shadow: 0 0 10px rgba(0, 212, 255, 0.8) !important;
    }

    /* 科幻按钮样式 */
    #userEquipmentApp .cyber-btn {
        background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(139, 92, 246, 0.2)) !important;
        border: 1px solid rgba(0, 212, 255, 0.5) !important;
        color: #00d4ff !important;
        padding: 0.75rem 1.5rem !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        position: relative !important;
        overflow: hidden !important;
        text-shadow: 0 0 5px rgba(0, 212, 255, 0.5) !important;
    }

    #userEquipmentApp .cyber-btn:hover {
        background: linear-gradient(135deg, rgba(0, 212, 255, 0.3), rgba(139, 92, 246, 0.3)) !important;
        border-color: #00d4ff !important;
        color: #ffffff !important;
        box-shadow: 0 0 20px rgba(0, 212, 255, 0.4) !important;
        transform: translateY(-2px) !important;
    }

    #userEquipmentApp .cyber-btn-secondary {
        background: linear-gradient(135deg, rgba(100, 116, 139, 0.2), rgba(71, 85, 105, 0.2)) !important;
        border: 1px solid rgba(100, 116, 139, 0.5) !important;
        color: #94a3b8 !important;
    }

    #userEquipmentApp .cyber-btn-secondary:hover {
        background: linear-gradient(135deg, rgba(100, 116, 139, 0.3), rgba(71, 85, 105, 0.3)) !important;
        border-color: #94a3b8 !important;
        color: #ffffff !important;
        box-shadow: 0 0 20px rgba(100, 116, 139, 0.4) !important;
    }

    #userEquipmentApp .cyber-btn-success {
        background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(16, 185, 129, 0.2)) !important;
        border: 1px solid rgba(34, 197, 94, 0.5) !important;
        color: #22c55e !important;
    }

    #userEquipmentApp .cyber-btn-success:hover {
        background: linear-gradient(135deg, rgba(34, 197, 94, 0.3), rgba(16, 185, 129, 0.3)) !important;
        border-color: #22c55e !important;
        color: #ffffff !important;
        box-shadow: 0 0 20px rgba(34, 197, 94, 0.4) !important;
    }

    #userEquipmentApp .cyber-btn-danger {
        background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 127, 0.2)) !important;
        border: 1px solid rgba(239, 68, 68, 0.5) !important;
        color: #ef4444 !important;
    }

    #userEquipmentApp .cyber-btn-danger:hover {
        background: linear-gradient(135deg, rgba(239, 68, 68, 0.3), rgba(220, 38, 127, 0.3)) !important;
        border-color: #ef4444 !important;
        color: #ffffff !important;
        box-shadow: 0 0 20px rgba(239, 68, 68, 0.4) !important;
    }

    /* 科幻表单样式 */
    #userEquipmentApp .cyber-form-group {
        margin-bottom: 1.5rem !important;
    }

    #userEquipmentApp .cyber-form-label {
        color: #00d4ff !important;
        font-weight: 500 !important;
        margin-bottom: 0.5rem !important;
        display: block !important;
        text-shadow: 0 0 5px rgba(0, 212, 255, 0.3) !important;
    }

    #userEquipmentApp .cyber-form-control {
        background: rgba(26, 26, 46, 0.8) !important;
        border: 1px solid rgba(0, 212, 255, 0.3) !important;
        border-radius: 8px !important;
        color: #e2e8f0 !important;
        padding: 0.75rem 1rem !important;
        transition: all 0.3s ease !important;
        width: 100% !important;
    }

    #userEquipmentApp .cyber-form-control:focus {
        border-color: #00d4ff !important;
        box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2) !important;
        outline: none !important;
        background: rgba(26, 26, 46, 0.9) !important;
    }

    #userEquipmentApp .cyber-form-control::placeholder {
        color: #64748b !important;
    }

    /* 科幻表格样式 */
    #userEquipmentApp .cyber-table {
        background: rgba(26, 26, 46, 0.95) !important;
        border: 1px solid rgba(0, 212, 255, 0.3) !important;
        border-radius: 12px !important;
        overflow: hidden !important;
        backdrop-filter: blur(10px) !important;
    }

    #userEquipmentApp .cyber-table thead th {
        background: rgba(0, 212, 255, 0.1) !important;
        border: none !important;
        color: #00d4ff !important;
        font-weight: 600 !important;
        padding: 1rem !important;
        text-transform: uppercase !important;
        font-size: 0.875rem !important;
        letter-spacing: 0.05em !important;
        text-shadow: 0 0 5px rgba(0, 212, 255, 0.5) !important;
        border-bottom: 1px solid rgba(0, 212, 255, 0.3) !important;
    }

    #userEquipmentApp .cyber-table tbody tr {
        border: none !important;
        transition: all 0.3s ease !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    #userEquipmentApp .cyber-table tbody tr:hover {
        background: rgba(0, 212, 255, 0.1) !important;
        transform: translateX(5px) !important;
    }

    #userEquipmentApp .cyber-table tbody td {
        border: none !important;
        padding: 1rem !important;
        vertical-align: middle !important;
        color: #e2e8f0 !important;
    }

    /* 科幻状态徽章 */
    #userEquipmentApp .cyber-badge {
        padding: 0.5rem 1rem !important;
        border-radius: 20px !important;
        font-weight: 500 !important;
        font-size: 0.875rem !important;
        text-transform: uppercase !important;
        letter-spacing: 0.05em !important;
        border: 1px solid !important;
        text-shadow: 0 0 5px rgba(0, 0, 0, 0.5) !important;
    }

    #userEquipmentApp .cyber-badge-equipped {
        background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(16, 185, 129, 0.2)) !important;
        color: #22c55e !important;
        border-color: rgba(34, 197, 94, 0.5) !important;
        box-shadow: 0 0 10px rgba(34, 197, 94, 0.3) !important;
    }

    #userEquipmentApp .cyber-badge-unequipped {
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(217, 119, 6, 0.2)) !important;
        color: #f59e0b !important;
        border-color: rgba(245, 158, 11, 0.5) !important;
        box-shadow: 0 0 10px rgba(245, 158, 11, 0.3) !important;
    }

    #userEquipmentApp .cyber-badge-level {
        background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(124, 58, 237, 0.2)) !important;
        color: #8b5cf6 !important;
        border-color: rgba(139, 92, 246, 0.5) !important;
        box-shadow: 0 0 10px rgba(139, 92, 246, 0.3) !important;
    }

    /* 强化等级徽章样式 */
    #userEquipmentApp .cyber-badge-common {
        background: linear-gradient(135deg, rgba(107, 114, 128, 0.2), rgba(75, 85, 99, 0.2)) !important;
        color: #9ca3af !important;
        border-color: rgba(107, 114, 128, 0.5) !important;
        box-shadow: 0 0 10px rgba(107, 114, 128, 0.3) !important;
    }

    #userEquipmentApp .cyber-badge-rare {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.2)) !important;
        color: #3b82f6 !important;
        border-color: rgba(59, 130, 246, 0.5) !important;
        box-shadow: 0 0 10px rgba(59, 130, 246, 0.3) !important;
    }

    #userEquipmentApp .cyber-badge-epic {
        background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(124, 58, 237, 0.2)) !important;
        color: #8b5cf6 !important;
        border-color: rgba(139, 92, 246, 0.5) !important;
        box-shadow: 0 0 10px rgba(139, 92, 246, 0.3) !important;
    }

    #userEquipmentApp .cyber-badge-legendary {
        background: linear-gradient(135deg, rgba(245, 101, 101, 0.2), rgba(239, 68, 68, 0.2)) !important;
        color: #f56565 !important;
        border-color: rgba(245, 101, 101, 0.5) !important;
        box-shadow: 0 0 15px rgba(245, 101, 101, 0.4) !important;
        animation: cyber-glow 2s ease-in-out infinite alternate !important;
    }

    /* 用户头像 */
    #userEquipmentApp .user-avatar {
        width: 32px !important;
        height: 32px !important;
        border-radius: 50% !important;
        background: linear-gradient(135deg, #00d4ff, #8b5cf6) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        color: white !important;
        font-weight: 600 !important;
        font-size: 0.9rem !important;
    }

    /* 装备图标 */
    #userEquipmentApp .equipment-icon {
        width: 28px !important;
        height: 28px !important;
        border-radius: 6px !important;
        background: linear-gradient(135deg, #f59e0b, #ef4444) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        color: white !important;
        font-size: 0.8rem !important;
    }

    /* 科幻分页 */
    #userEquipmentApp .cyber-pagination .page-link {
        background: rgba(26, 26, 46, 0.8) !important;
        border: 1px solid rgba(0, 212, 255, 0.3) !important;
        color: #00d4ff !important;
        margin: 0 0.25rem !important;
        border-radius: 8px !important;
        padding: 0.75rem 1rem !important;
        transition: all 0.3s ease !important;
    }

    #userEquipmentApp .cyber-pagination .page-link:hover {
        background: rgba(0, 212, 255, 0.2) !important;
        border-color: #00d4ff !important;
        color: #ffffff !important;
        box-shadow: 0 0 10px rgba(0, 212, 255, 0.4) !important;
    }

    #userEquipmentApp .cyber-pagination .page-item.active .page-link {
        background: linear-gradient(135deg, rgba(0, 212, 255, 0.3), rgba(139, 92, 246, 0.3)) !important;
        border-color: #00d4ff !important;
        color: #ffffff !important;
        box-shadow: 0 0 15px rgba(0, 212, 255, 0.5) !important;
    }

    /* 加载动画 */
    #userEquipmentApp .cyber-loading {
        display: inline-block !important;
        width: 20px !important;
        height: 20px !important;
        border: 3px solid rgba(0, 212, 255, 0.3) !important;
        border-radius: 50% !important;
        border-top-color: #00d4ff !important;
        animation: cyber-spin 1s ease-in-out infinite !important;
    }

    @@keyframes cyber-spin {
        to { transform: rotate(360deg); }
    }

    /* 科幻背景 */
    .cyber-bg {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%) !important;
        z-index: -1 !important;
    }

    .cyber-bg::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background-image:
            radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%) !important;
        animation: pulse 4s ease-in-out infinite alternate !important;
    }

    @@keyframes pulse {
        0% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    /* 科幻模态框样式 */
    /* 确保模态框在最顶层 */
    #userEquipmentApp .modal {
        z-index: 9999 !important;
        position: fixed !important;
    }

    #userEquipmentApp .modal-backdrop {
        display: none !important;
    }

    /* 确保Vue应用容器不会影响模态框层级 */
    #userEquipmentApp {
        position: relative;
        z-index: 1;
    }

    #userEquipmentApp .cyber-modal .modal-content {
        background: rgba(26, 26, 46, 0.95) !important;
        border: 1px solid rgba(0, 212, 255, 0.3) !important;
        border-radius: 12px !important;
        box-shadow:
            0 20px 60px rgba(0, 0, 0, 0.5),
            inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(10px) !important;
    }

    #userEquipmentApp .cyber-modal .modal-header {
        background: rgba(0, 212, 255, 0.1) !important;
        border-bottom: 1px solid rgba(0, 212, 255, 0.3) !important;
        border-radius: 12px 12px 0 0 !important;
    }

    #userEquipmentApp .cyber-modal .modal-title {
        color: #00d4ff !important;
        font-weight: 600 !important;
        text-shadow: 0 0 10px rgba(0, 212, 255, 0.5) !important;
    }

    #userEquipmentApp .cyber-modal .btn-close {
        background: none !important;
        border: none !important;
        font-size: 1.5rem !important;
        color: #00d4ff !important;
        padding: 0.5rem !important;
        width: auto !important;
        height: auto !important;
        opacity: 0.7 !important;
        filter: none !important;
        transition: all 0.3s ease !important;
    }

    #userEquipmentApp .cyber-modal .btn-close:hover {
        opacity: 1 !important;
        color: #ffffff !important;
        background: rgba(0, 212, 255, 0.2) !important;
        border-radius: 4px !important;
        transform: scale(1.1) !important;
    }

    #userEquipmentApp .cyber-modal .btn-close i {
        color: inherit !important;
        font-size: 1.2rem !important;
    }
}
</style>

<!-- 科幻背景 -->
<div class="cyber-bg"></div>

<!-- 科幻用户装备管理应用容器 -->
<div id="userEquipmentApp">
    <!-- 科幻页面标题 -->
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-12">
                <div class="cyber-card">
                    <div class="cyber-card-header">
                        <h1 class="cyber-card-title">
                            <i class="fas fa-shield-alt"></i>
                            用户装备管理系统
                        </h1>
                        <nav aria-label="breadcrumb" class="mt-2">
                            <ol class="breadcrumb mb-0" style="background: transparent;">
                                <li class="breadcrumb-item">
                                    <a href="/" style="color: #00d4ff; text-decoration: none;">
                                        <i class="fas fa-home me-1"></i>控制中心
                                    </a>
                                </li>
                                <li class="breadcrumb-item active" style="color: #94a3b8;">
                                    装备管理
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻主要内容 -->
    <div class="container-fluid">

        <!-- 科幻搜索条件 -->
        <div class="cyber-card mb-4">
            <div class="cyber-card-header">
                <h3 class="cyber-card-title">
                    <i class="fas fa-search"></i>
                    搜索过滤器
                </h3>
            </div>
            <div class="p-4">
                <div class="row">
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="searchUserId">用户ID</label>
                            <input type="number" class="cyber-form-control" id="searchUserId" v-model="queryForm.userId" placeholder="输入用户ID">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="searchUserName">用户名</label>
                            <input type="text" class="cyber-form-control" id="searchUserName" v-model="queryForm.userName" placeholder="输入用户名">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="searchEquipmentName">装备名称</label>
                            <input type="text" class="cyber-form-control" id="searchEquipmentName" v-model="queryForm.name" placeholder="输入装备名称">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="searchEquipmentType">装备类型</label>
                            <select class="cyber-form-control" id="searchEquipmentType" v-model="queryForm.equipTypeId">
                                <option value="">全部类型</option>
                                <option v-for="option in equipmentTypeOptions" v-bind:key="option.value" v-bind:value="option.value">{{ option.label }}</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="searchEquipmentStatus">装备状态</label>
                            <select class="cyber-form-control" id="searchEquipmentStatus" v-model="queryForm.isEquipped">
                                <option value="">全部状态</option>
                                <option value="true">已装备</option>
                                <option value="false">未装备</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="searchMinLevel">最低强化等级</label>
                            <input type="number" class="cyber-form-control" id="searchMinLevel" v-model="queryForm.minStrengthenLevel" placeholder="最低等级" min="0" max="20">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="searchMaxLevel">最高强化等级</label>
                            <input type="number" class="cyber-form-control" id="searchMaxLevel" v-model="queryForm.maxStrengthenLevel" placeholder="最高等级" min="0" max="20">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="button" class="cyber-btn" v-on:click="searchEquipments">
                                    <i class="fas fa-search me-1"></i>搜索
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-secondary" v-on:click="resetSearch">
                                    <i class="fas fa-redo me-1"></i>重置
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-success" onclick="showAddModal()">
                                    <i class="fas fa-plus me-1"></i>新增装备
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科幻装备列表 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <h3 class="cyber-card-title">
                    <i class="fas fa-list"></i>
                    装备数据库
                </h3>
            </div>
            <div class="table-responsive p-0">
                <table class="cyber-table table text-nowrap">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户</th>
                            <th>装备名称</th>
                            <th>装备类型</th>
                            <th>强化等级</th>
                            <th>槽位</th>
                            <th>位置</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody v-if="loading">
                        <tr>
                            <td colspan="10" class="text-center py-4">
                                <div class="cyber-loading me-2"></div>数据加载中...
                            </td>
                        </tr>
                    </tbody>
                    <tbody v-else-if="equipmentsList.length === 0">
                        <tr>
                            <td colspan="10" class="text-center py-4" style="color: #64748b;">
                                <i class="fas fa-database me-2"></i>暂无装备数据
                            </td>
                        </tr>
                    </tbody>
                    <tbody v-else>
                        <tr v-for="equipment in equipmentsList" v-bind:key="equipment.id" v-bind:data-equipment-id="equipment.id">
                            <td>{{ equipment.id }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar me-2">
                                        {{ equipment.userName ? equipment.userName.charAt(0).toUpperCase() : 'U' }}
                                    </div>
                                    <div>
                                        <div style="color: #64748b; font-size: 0.8rem;">#{{ equipment.userId }}</div>
                                        <div style="font-weight: 500;">{{ equipment.userName }}</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="equipment-icon me-2">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <span>{{ equipment.name }}</span>
                                </div>
                            </td>
                            <td>{{ equipment.equipTypeName }}</td>
                            <td>
                                <span class="cyber-badge cyber-badge-level">
                                    +{{ equipment.strengthenLevel }}
                                </span>
                            </td>
                            <td>{{ equipment.slot }}</td>
                            <td>{{ equipment.position }}</td>
                            <td>
                                <span v-if="equipment.isEquipped" class="cyber-badge cyber-badge-equipped">
                                    <i class="fas fa-check me-1"></i>已装备
                                </span>
                                <span v-else class="cyber-badge cyber-badge-unequipped">
                                    <i class="fas fa-times me-1"></i>未装备
                                </span>
                            </td>
                            <td style="color: #94a3b8; font-size: 0.9rem;">{{ formatDateTime(equipment.createTime) }}</td>
                            <td>
                                <div class="d-flex gap-1">
                                    <button type="button" class="cyber-btn py-1 px-2" style="font-size: 0.8rem;" v-on:click="viewDetail(equipment)" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="cyber-btn cyber-btn-secondary py-1 px-2" style="font-size: 0.8rem;" onclick="showEditModal(this)" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="cyber-btn py-1 px-2" style="font-size: 0.8rem; background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(124, 58, 237, 0.2)) !important; border-color: rgba(139, 92, 246, 0.5) !important; color: #8b5cf6 !important;" v-on:click="showStrengthenModal(equipment)" title="强化">
                                        <i class="fas fa-hammer"></i>
                                    </button>
                                    <button v-if="!equipment.isEquipped" type="button" class="cyber-btn cyber-btn-success py-1 px-2" style="font-size: 0.8rem;" v-on:click="wearEquipment(equipment)" title="穿戴">
                                        <i class="fas fa-plus-circle"></i>
                                    </button>
                                    <button v-else type="button" class="cyber-btn cyber-btn-secondary py-1 px-2" style="font-size: 0.8rem;" v-on:click="unwearEquipment(equipment)" title="卸下">
                                        <i class="fas fa-minus-circle"></i>
                                    </button>
                                    <button type="button" class="cyber-btn cyber-btn-danger py-1 px-2" style="font-size: 0.8rem;" v-on:click="deleteEquipment(equipment)" v-bind:disabled="equipment.isEquipped" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                            </tr>
                        </tbody>
                    </table>
            </div>

            <!-- 科幻分页 -->
            <div class="p-4 border-top" style="border-color: rgba(0, 212, 255, 0.3) !important;" v-if="equipmentsList.length > 0">
                <div class="row align-items-center">
                    <div class="col-sm-6">
                        <div style="color: #94a3b8; font-size: 0.9rem;">
                            显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条记录，共 {{ totalCount }} 条记录
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="d-flex justify-content-end">
                            <ul class="pagination cyber-pagination mb-0">
                                <li class="page-item" v-bind:class="{ disabled: currentPage === 1 }">
                                    <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage - 1)">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                                <li v-for="page in visiblePages" v-bind:key="page" class="page-item" v-bind:class="{ active: page === currentPage }">
                                    <a href="#" class="page-link" v-on:click.prevent="changePage(page)">{{ page }}</a>
                                </li>
                                <li class="page-item" v-bind:class="{ disabled: currentPage === totalPages }">
                                    <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage + 1)">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
            
    </div>
  
 

    <!-- 科幻新增/编辑装备模态框 -->
    <div class="modal fade cyber-modal" id="equipmentModal" tabindex="-1" role="dialog" aria-labelledby="equipmentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="equipmentModalLabel">
                        <i class="fas fa-shield-alt me-2"></i><span id="modalTitle">新增装备</span>
                    </h5>
                    <button type="button" class="btn-close" onclick="closeModal()" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalUserId">所属用户 <span style="color: #ef4444;">*</span></label>
                                    <select class="cyber-form-control" id="modalUserId" v-model="equipmentForm.userId" required>
                                        <option value="">请选择用户</option>
                                        <!-- 用户选项将通过JavaScript动态填充 -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalEquipId">装备配置 <span style="color: #ef4444;">*</span></label>
                                    <div class="custom-select-container" id="equipmentSelectContainer">
                                        <input
                                            type="text"
                                            class="cyber-form-control"
                                            id="selectedEquipmentDisplay"
                                            onclick="toggleEquipmentDropdown()"
                                            placeholder="请选择装备配置"
                                            readonly
                                        >
                                        <i class="fas fa-chevron-down" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #00d4ff; pointer-events: none;"></i>
                                        <div class="custom-select-dropdown" id="equipmentDropdown" style="position: absolute; top: 100%; left: 0; right: 0; background: rgba(26, 26, 46, 0.95); border: 1px solid rgba(0, 212, 255, 0.3); border-radius: 8px; max-height: 300px; overflow-y: auto; z-index: 1000; display: none;">
                                            <input
                                                type="text"
                                                class="cyber-form-control"
                                                id="equipmentSearchText"
                                                placeholder="搜索装备名称或编号..."
                                                oninput="filterEquipments()"
                                                style="border-radius: 8px 8px 0 0; border-bottom: 1px solid rgba(0, 212, 255, 0.3);"
                                            >
                                            <div id="equipmentOptions">
                                                <!-- 装备选项将通过JavaScript动态填充 -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalStrengthenLevel">强化等级</label>
                                    <input type="number" class="cyber-form-control" id="modalStrengthenLevel" min="0" max="20" placeholder="0-20">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalSlot">槽位</label>
                                    <input type="number" class="cyber-form-control" id="modalSlot" min="1" placeholder="槽位编号">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalPosition">位置</label>
                                    <input type="number" class="cyber-form-control" id="modalPosition" min="1" placeholder="位置编号">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalIsEquipped">装备状态</label>
                                    <select class="cyber-form-control" id="modalIsEquipped">
                                        <option value="false">未装备</option>
                                        <option value="true">已装备</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-secondary" onclick="closeModal()">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="cyber-btn" id="saveEquipmentBtn" onclick="saveEquipment()">
                        <i class="fas fa-save me-1"></i>
                        <span id="saveButtonText">保存</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻装备详情模态框 -->
    <div class="modal fade cyber-modal" id="equipmentDetailModal" tabindex="-1" role="dialog" aria-labelledby="equipmentDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="equipmentDetailModalLabel">
                        <i class="fas fa-info-circle me-2"></i>装备详情
                    </h5>
                    <button type="button" class="btn-close" v-on:click="closeDetailModal" aria-label="Close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body" v-if="equipmentDetail">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">基础信息</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>装备ID:</strong></td>
                                    <td v-text="equipmentDetail.id"></td>
                                </tr>
                                <tr>
                                    <td><strong>用户:</strong></td>
                                    <td>
                                        <span v-text="equipmentDetail.userName"></span>
                                        (#<span v-text="equipmentDetail.userId"></span>)
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>装备名称:</strong></td>
                                    <td v-text="equipmentDetail.name"></td>
                                </tr>
                                <tr>
                                    <td><strong>装备类型:</strong></td>
                                    <td v-text="equipmentDetail.equipTypeName"></td>
                                </tr>
                                <tr>
                                    <td><strong>强化等级:</strong></td>
                                    <td>
                                        <span v-bind:class="getStrengthenBadgeClass(equipmentDetail.strengthenLevel)">
                                            +<span v-text="equipmentDetail.strengthenLevel"></span>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>槽位:</strong></td>
                                    <td v-text="equipmentDetail.slot"></td>
                                </tr>
                                <tr>
                                    <td><strong>位置:</strong></td>
                                    <td v-text="equipmentDetail.position"></td>
                                </tr>
                                <tr>
                                    <td><strong>状态:</strong></td>
                                    <td>
                                        <span v-if="equipmentDetail.isEquipped" class="badge-success">已装备</span>
                                        <span v-else class="badge-secondary">未装备</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6" v-if="equipmentDetail.detail">
                            <h6 class="text-success">装备属性</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>攻击力:</strong></td>
                                    <td class="text-danger" v-text="equipmentDetail.detail.atk"></td>
                                </tr>
                                <tr>
                                    <td><strong>命中:</strong></td>
                                    <td class="text-warning" v-text="equipmentDetail.detail.hit"></td>
                                </tr>
                                <tr>
                                    <td><strong>防御力:</strong></td>
                                    <td class="text-primary" v-text="equipmentDetail.detail.def"></td>
                                </tr>
                                <tr>
                                    <td><strong>速度:</strong></td>
                                    <td class="text-info" v-text="equipmentDetail.detail.spd"></td>
                                </tr>
                                <tr>
                                    <td><strong>闪避:</strong></td>
                                    <td class="text-success" v-text="equipmentDetail.detail.dodge"></td>
                                </tr>
                                <tr>
                                    <td><strong>生命值:</strong></td>
                                    <td class="text-danger" v-text="equipmentDetail.detail.hp"></td>
                                </tr>
                                <tr>
                                    <td><strong>魔法值:</strong></td>
                                    <td class="text-primary" v-text="equipmentDetail.detail.mp"></td>
                                </tr>
                                <tr>
                                    <td><strong>深度:</strong></td>
                                    <td v-text="equipmentDetail.detail.deepen"></td>
                                </tr>
                                <tr>
                                    <td><strong>偏移:</strong></td>
                                    <td v-text="equipmentDetail.detail.offset"></td>
                                </tr>
                                <tr>
                                    <td><strong>吸血:</strong></td>
                                    <td v-text="equipmentDetail.detail.vamp"></td>
                                </tr>
                                <tr>
                                    <td><strong>吸魔:</strong></td>
                                    <td v-text="equipmentDetail.detail.vampMp"></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row" v-if="equipmentDetail.detail && equipmentDetail.detail.description">
                        <div class="col-12">
                            <h6 class="text-info">装备描述</h6>
                            <p class="text-muted" v-text="equipmentDetail.detail.description"></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" v-on:click="closeDetailModal">
                        <i class="fas fa-times mr-1"></i>关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻强化装备模态框 -->
    <div class="modal fade cyber-modal" id="strengthenModal" tabindex="-1" role="dialog" aria-labelledby="strengthenModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="strengthenModalLabel">
                        <i class="fas fa-hammer me-2"></i>强化装备
                    </h5>
                    <button type="button" class="btn-close" v-on:click="closeStrengthenModal" aria-label="Close"></button>
                </div>
                <div class="modal-body" v-if="selectedEquipment">
                    <div class="alert" style="background: rgba(0, 212, 255, 0.1); border: 1px solid rgba(0, 212, 255, 0.3); border-radius: 8px; color: #e2e8f0; padding: 1rem;">
                        <i class="fas fa-info-circle me-2" style="color: #00d4ff;"></i>
                        当前装备：<strong style="color: #00d4ff;">{{ selectedEquipment.name }}</strong><br>
                        当前强化等级：<span class="cyber-badge cyber-badge-level">+{{ selectedEquipment.strengthenLevel }}</span>
                    </div>
                    <div class="cyber-form-group">
                        <label class="cyber-form-label" for="targetLevel">目标强化等级 <span style="color: #ef4444;">*</span></label>
                        <input type="number" class="cyber-form-control" id="targetLevel" v-model="strengthenForm.targetLevel"
                               v-bind:min="selectedEquipment.strengthenLevel + 1" max="20" required>
                        <small style="color: #94a3b8; font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                            强化等级范围：{{ selectedEquipment.strengthenLevel + 1 }} - 20
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-secondary" v-on:click="closeStrengthenModal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="cyber-btn" style="background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(217, 119, 6, 0.2)) !important; border-color: rgba(245, 158, 11, 0.5) !important; color: #f59e0b !important;" v-on:click="confirmStrengthen" v-bind:disabled="saving">
                        <i v-if="saving" class="cyber-loading me-1"></i>
                        <i v-else class="fas fa-hammer me-1"></i>
                        {{ saving ? '强化中...' : '确认强化' }}
                    </button>
                </div>
            </div>
        </div>
    </div>

   </div>
@section Scripts {
    <script>
        // 全局变量
        let isEdit = false;
        let equipmentForm = {};
        let userOptions = [];
        let allEquipments = [];
        let filteredEquipments = [];
        let showEquipmentDropdown = false;
        let equipmentSearchText = '';
        let saving = false;

        // 全局函数 - 显示新增模态框
        function showAddModal() {
            isEdit = false;
            equipmentForm = {
                id: 0,
                userId: null,
                equipId: null,
                name: '',
                icon: '',
                equipTypeId: null,
                strengthenLevel: 0,
                slot: 1,
                position: 1,
                isEquipped: false
            };

            // 更新模态框标题
            document.getElementById('modalTitle').textContent = '新增装备';

            // 重置表单
            resetModalForm();

            // 重置装备搜索相关状态
            showEquipmentDropdown = false;
            equipmentSearchText = '';
            filteredEquipments = allEquipments;

            $('#equipmentModal').modal({backdrop: false, show: true});
        }

        // 全局函数 - 显示编辑模态框
        function showEditModal(button) {
            // 从按钮的父行获取装备数据
            const row = button.closest('tr');
            const equipmentId = row.getAttribute('data-equipment-id');

            // 从Vue实例获取装备数据
            const equipment = window.app.equipmentsList.find(e => e.id == equipmentId);
            if (!equipment) return;

            isEdit = true;
            equipmentForm = {
                id: equipment.id,
                userId: equipment.userId,
                equipId: equipment.equipId,
                name: equipment.name,
                icon: equipment.icon,
                equipTypeId: equipment.equipTypeId,
                strengthenLevel: equipment.strengthenLevel,
                slot: equipment.slot,
                position: equipment.position,
                isEquipped: equipment.isEquipped
            };

            // 更新模态框标题
            document.getElementById('modalTitle').textContent = '编辑装备';

            // 填充表单
            fillModalForm();

            // 重置装备搜索相关状态
            showEquipmentDropdown = false;
            equipmentSearchText = '';
            filteredEquipments = allEquipments;

            $('#equipmentModal').modal({backdrop: false, show: true});
        }

        // 全局函数 - 保存装备
        async function saveEquipment() {
            try {
                saving = true;
                updateSaveButton(true);

                // 从表单获取数据
                getFormData();

                const url = isEdit ? '/UserEquipment/Update' : '/UserEquipment/Create';
                const response = await axios.post(url, equipmentForm);

                if (response.data.code === 200) {
                    $('#equipmentModal').modal('hide');
                    if (window.app && window.app.loadEquipments) {
                        window.app.loadEquipments();
                    }
                    alert(response.data.message || (isEdit ? '更新成功' : '创建成功'));
                } else {
                    alert(response.data.message || '操作失败');
                }
            } catch (error) {
                console.error('保存装备失败：', error);
                alert('保存失败，请重试');
            } finally {
                saving = false;
                updateSaveButton(false);
            }
        }

        // 全局函数 - 关闭模态框
        function closeModal() {
            // 重置装备搜索相关状态
            showEquipmentDropdown = false;
            equipmentSearchText = '';

            $('#equipmentModal').modal('hide');
        }

        // 辅助函数 - 重置模态框表单
        function resetModalForm() {
            document.getElementById('modalUserId').value = '';
            document.getElementById('selectedEquipmentDisplay').value = '';
            document.getElementById('modalStrengthenLevel').value = '0';
            document.getElementById('modalSlot').value = '1';
            document.getElementById('modalPosition').value = '1';
            document.getElementById('modalIsEquipped').value = 'false';
        }

        // 辅助函数 - 填充模态框表单
        function fillModalForm() {
            document.getElementById('modalUserId').value = equipmentForm.userId || '';
            document.getElementById('modalStrengthenLevel').value = equipmentForm.strengthenLevel || 0;
            document.getElementById('modalSlot').value = equipmentForm.slot || 1;
            document.getElementById('modalPosition').value = equipmentForm.position || 1;
            document.getElementById('modalIsEquipped').value = equipmentForm.isEquipped ? 'true' : 'false';

            // 设置装备选择显示
            if (equipmentForm.equipId) {
                const equipment = allEquipments.find(e => e.value == equipmentForm.equipId);
                if (equipment) {
                    document.getElementById('selectedEquipmentDisplay').value = equipment.label;
                }
            }
        }

        // 辅助函数 - 从表单获取数据
        function getFormData() {
            equipmentForm.userId = parseInt(document.getElementById('modalUserId').value) || null;
            equipmentForm.strengthenLevel = parseInt(document.getElementById('modalStrengthenLevel').value) || 0;
            equipmentForm.slot = parseInt(document.getElementById('modalSlot').value) || 1;
            equipmentForm.position = parseInt(document.getElementById('modalPosition').value) || 1;
            equipmentForm.isEquipped = document.getElementById('modalIsEquipped').value === 'true';
        }

        // 辅助函数 - 更新保存按钮状态
        function updateSaveButton(isSaving) {
            const btn = document.getElementById('saveEquipmentBtn');
            const text = document.getElementById('saveButtonText');
            const icon = btn.querySelector('i');

            if (isSaving) {
                btn.disabled = true;
                icon.className = 'cyber-loading me-1';
                text.textContent = '保存中...';
            } else {
                btn.disabled = false;
                icon.className = 'fas fa-save me-1';
                text.textContent = '保存';
            }
        }

        // 装备下拉框相关函数
        function toggleEquipmentDropdown() {
            showEquipmentDropdown = !showEquipmentDropdown;
            const dropdown = document.getElementById('equipmentDropdown');
            const container = document.getElementById('equipmentSelectContainer');

            if (showEquipmentDropdown) {
                dropdown.style.display = 'block';
                container.classList.add('open');
                renderEquipmentOptions();
            } else {
                dropdown.style.display = 'none';
                container.classList.remove('open');
            }
        }

        function filterEquipments() {
            const searchText = document.getElementById('equipmentSearchText').value.toLowerCase();
            equipmentSearchText = searchText;

            if (searchText) {
                filteredEquipments = allEquipments.filter(equipment =>
                    equipment.label.toLowerCase().includes(searchText) ||
                    equipment.value.toString().includes(searchText)
                );
            } else {
                filteredEquipments = allEquipments;
            }

            renderEquipmentOptions();
        }

        function selectEquipment(equipment) {
            equipmentForm.equipId = equipment.value;
            equipmentForm.name = equipment.label;
            equipmentForm.icon = equipment.icon;
            equipmentForm.equipTypeId = equipment.equipTypeId;

            document.getElementById('selectedEquipmentDisplay').value = equipment.label;
            toggleEquipmentDropdown();
        }

        function renderEquipmentOptions() {
            const container = document.getElementById('equipmentOptions');

            if (!filteredEquipments || filteredEquipments.length === 0) {
                container.innerHTML = '<div class="custom-select-option" style="color: #64748b; text-align: center; padding: 0.75rem;">未找到匹配的装备</div>';
                return;
            }

            container.innerHTML = filteredEquipments.map(equipment => `
                <div class="custom-select-option ${equipmentForm.equipId == equipment.value ? 'selected' : ''}"
                     onclick="selectEquipment(${JSON.stringify(equipment).replace(/"/g, '&quot;')})"
                     style="padding: 0.75rem; cursor: pointer; border-bottom: 1px solid rgba(255, 255, 255, 0.1); transition: all 0.3s ease;">
                    <div style="color: #e2e8f0; font-weight: 500;">${equipment.label}</div>
                    <div style="color: #94a3b8; font-size: 0.8rem; margin-top: 0.25rem;">
                        类型: ${getEquipmentTypeName(equipment.equipTypeId)} | 编号: ${equipment.value}
                    </div>
                </div>
            `).join('');
        }

        function getEquipmentTypeName(typeId) {
            const typeNames = {
                1: '武器',
                2: '防具',
                3: '饰品'
            };
            return typeNames[typeId] || '未知';
        }

        // 更新用户下拉框
        function updateUserSelect() {
            const select = document.getElementById('modalUserId');
            if (!select) return;

            // 清空现有选项（保留第一个默认选项）
            select.innerHTML = '<option value="">请选择用户</option>';

            // 添加用户选项
            userOptions.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.label;
                select.appendChild(optionElement);
            });
        }

        // 确保Vue和axios已加载
        if (typeof Vue === 'undefined') {
            console.error('Vue.js未加载！请检查_Layout.cshtml中的Vue.js引用');
            document.getElementById('userEquipmentApp').innerHTML = '<div class="alert alert-danger">Vue.js加载失败，请刷新页面重试</div>';
        } else if (typeof axios === 'undefined') {
            console.error('axios未加载！请检查_Layout.cshtml中的axios引用');
            document.getElementById('userEquipmentApp').innerHTML = '<div class="alert alert-danger">axios加载失败，请刷新页面重试</div>';
        } else {
            console.log('Vue和axios加载成功，开始初始化应用...');

            const { createApp } = Vue;

        window.app = createApp({
            data() {
                return {
                    // 加载状态
                    loading: false,
                    saving: false,
                    
                    // 装备列表数据
                    equipmentsList: [],
                    totalCount: 0,
                    currentPage: 1,
                    pageSize: 10,
                    
                    // 表单状态
                    isEdit: false,
                    selectedEquipment: null,
                    equipmentDetail: null,
                    
                    // 下拉选项
                    userOptions: [],
                    equipmentTypeOptions: [],
                    equipmentConfigOptions: [],

                    // 装备搜索下拉框相关
                    showEquipmentDropdown: false,
                    equipmentSearchText: '',
                    filteredEquipments: [],
                    allEquipments: [], // 存储所有装备配置，用于过滤
                    
                    // 查询表单
                    queryForm: {
                        userId: null,
                        userName: '',
                        name: '',
                        equipTypeId: '',
                        isEquipped: '',
                        minStrengthenLevel: null,
                        maxStrengthenLevel: null
                    },
                    
                    // 装备表单
                    equipmentForm: {
                        id: 0,
                        userId: null,
                        equipId: null,
                        name: '',
                        icon: '',
                        equipTypeId: null,
                        strengthenLevel: 0,
                        slot: 1,
                        position: 1,
                        isEquipped: false
                    },
                    
                    // 强化表单
                    strengthenForm: {
                        id: 0,
                        targetLevel: 1
                    }
                };
            },
            
            computed: {
                // 总页数
                totalPages() {
                    return Math.ceil(this.totalCount / this.pageSize);
                },
                
                // 可见页码
                visiblePages() {
                    const total = this.totalPages;
                    const current = this.currentPage;
                    const delta = 2;
                    const pages = [];
                    
                    for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
                        pages.push(i);
                    }
                    
                    if (current - delta > 2) {
                        pages.unshift("...");
                    }
                    if (current + delta < total - 1) {
                        pages.push("...");
                    }
                    
                    pages.unshift(1);
                    if (total > 1) {
                        pages.push(total);
                    }
                    
                    return pages.filter((page, index, arr) => arr.indexOf(page) === index);
                },

                // 装备选择下拉框的显示文本
                selectedEquipmentDisplay() {
                    if (!this.equipmentForm.equipId) {
                        return '请选择装备配置';
                    }
                    const config = this.equipmentConfigOptions.find(c => c.value == this.equipmentForm.equipId);
                    if (!config) {
                        return this.equipmentForm.equipId; // 如果没有找到，显示编号
                    }
                    return config.label;
                }
            },
            
            methods: {
                // 加载装备列表
                async loadEquipments() {
                    try {
                        this.loading = true;
                        
                        // 准备请求数据，确保字段名与后端DTO匹配
                        const requestData = {
                            UserId: this.queryForm.userId,
                            UserName: this.queryForm.userName || '',
                            Name: this.queryForm.name || '',
                            EquipTypeId: this.queryForm.equipTypeId || null,  // 保持字符串类型，不转换为整数
                            IsEquipped: this.queryForm.isEquipped === '' ? null : (this.queryForm.isEquipped === 'true'),
                            MinStrengthenLevel: this.queryForm.minStrengthenLevel,
                            MaxStrengthenLevel: this.queryForm.maxStrengthenLevel,
                            Page: this.currentPage,
                            PageSize: this.pageSize
                        };

                        // 添加调试日志
                        console.log('发送请求数据:', requestData);
                        
                        const response = await axios.post('/UserEquipment/GetList', requestData);
                        if (response.data.code === 200) {
                            this.equipmentsList = response.data.data || [];
                            this.totalCount = response.data.total || 0;
                        } else {
                            console.error('获取装备列表失败：', response.data.message);
                        }
                    } catch (error) {
                        console.error('获取装备列表失败：', error);
                    } finally {
                        this.loading = false;
                    }
                },
                
                // 搜索装备
                searchEquipments() {
                    console.log('搜索参数:', this.queryForm);
                    console.log('装备类型ID:', this.queryForm.equipTypeId, '类型:', typeof this.queryForm.equipTypeId);
                    this.currentPage = 1;
                    this.loadEquipments();
                },
                
                // 重置搜索
                resetSearch() {
                    Object.assign(this.queryForm, {
                        userId: null,
                        userName: '',
                        name: '',
                        equipTypeId: '',
                        isEquipped: '',
                        minStrengthenLevel: null,
                        maxStrengthenLevel: null
                    });
                    this.searchEquipments();
                },
                
                // 分页
                changePage(page) {
                    if (page >= 1 && page <= this.totalPages) {
                        this.currentPage = page;
                        this.loadEquipments();
                    }
                },
                
                // 显示新增模态框
                showAddModal() {
                    this.isEdit = false;
                    this.equipmentForm = {
                        id: 0,
                        userId: null,
                        equipId: null,
                        name: '',
                        icon: '',
                        equipTypeId: null,
                        strengthenLevel: 0,
                        slot: 1,
                        position: 1,
                        isEquipped: false
                    };

                    // 重置装备搜索相关状态
                    this.showEquipmentDropdown = false;
                    this.equipmentSearchText = '';
                    this.filteredEquipments = this.allEquipments;

                    $('#equipmentModal').modal({backdrop: false, show: true});
                },
                
                // 显示编辑模态框
                showEditModal(equipment) {
                    this.isEdit = true;
                    this.equipmentForm = {
                        id: equipment.id,
                        userId: equipment.userId,
                        equipId: equipment.equipId,
                        name: equipment.name,
                        icon: equipment.icon,
                        equipTypeId: equipment.equipTypeId,
                        strengthenLevel: equipment.strengthenLevel,
                        slot: equipment.slot,
                        position: equipment.position,
                        isEquipped: equipment.isEquipped
                    };

                    // 重置装备搜索相关状态
                    this.showEquipmentDropdown = false;
                    this.equipmentSearchText = '';
                    this.filteredEquipments = this.allEquipments;

                    $('#equipmentModal').modal({backdrop: false, show: true});
                },
                
                // 保存装备
                async saveEquipment() {
                    try {
                        this.saving = true;
                        
                        const url = this.isEdit ? '/UserEquipment/Update' : '/UserEquipment/Create';
                        const response = await axios.post(url, this.equipmentForm);
                        
                        if (response.data.code === 200) {
                            $('#equipmentModal').modal('hide');
                            this.loadEquipments();
                            alert(response.data.message || (this.isEdit ? '更新成功' : '创建成功'));
                        } else {
                            alert(response.data.message || '操作失败');
                        }
                    } catch (error) {
                        console.error('保存装备失败：', error);
                        alert('保存失败，请重试');
                    } finally {
                        this.saving = false;
                    }
                },
                
                // 删除装备
                async deleteEquipment(equipment) {
                    if (equipment.isEquipped) {
                        alert('已装备的装备无法删除，请先卸下装备');
                        return;
                    }
                    
                    if (!confirm(`确定要删除装备"${equipment.name}"吗？此操作无法撤销！`)) {
                        return;
                    }
                    
                    try {
                        const response = await axios.post('/UserEquipment/Delete', { id: equipment.id });
                        if (response.data.code === 200) {
                            this.loadEquipments();
                            alert('删除成功');
                        } else {
                            alert(response.data.message || '删除失败');
                        }
                    } catch (error) {
                        console.error('删除装备失败：', error);
                        alert('删除失败，请重试');
                    }
                },
                
                // 查看装备详情
                async viewDetail(equipment) {
                    try {
                        const response = await axios.get(`/UserEquipment/GetById/${equipment.id}`);
                        if (response.data.code === 200) {
                            this.equipmentDetail = response.data.data;
                            $('#equipmentDetailModal').modal({backdrop: false, show: true});
                        } else {
                            alert(response.data.message || '获取装备详情失败');
                        }
                    } catch (error) {
                        console.error('获取装备详情失败：', error);
                        alert('获取装备详情失败，请重试');
                    }
                },
                
                // 显示强化模态框
                showStrengthenModal(equipment) {
                    this.selectedEquipment = equipment;
                    this.strengthenForm = {
                        id: equipment.id,
                        targetLevel: equipment.strengthenLevel + 1
                    };
                    $('#strengthenModal').modal({backdrop: false, show: true});
                },
                
                // 确认强化
                async confirmStrengthen() {
                    if (!confirm(`确定要将装备强化到 +${this.strengthenForm.targetLevel} 级吗？`)) {
                        return;
                    }
                    
                    try {
                        this.saving = true;
                        const response = await axios.post('/UserEquipment/Strengthen', this.strengthenForm);
                        
                        if (response.data.code === 200) {
                            $('#strengthenModal').modal('hide');
                            this.loadEquipments();
                            alert(response.data.message || '强化成功');
                        } else {
                            alert(response.data.message || '强化失败');
                        }
                    } catch (error) {
                        console.error('强化装备失败：', error);
                        alert('强化失败，请重试');
                    } finally {
                        this.saving = false;
                    }
                },
                
                // 穿戴装备
                async wearEquipment(equipment) {
                    if (!confirm(`确定要穿戴装备"${equipment.name}"吗？`)) {
                        return;
                    }
                    
                    try {
                        const response = await axios.post('/UserEquipment/Wear', {
                            id: equipment.id,
                            userId: equipment.userId,
                            position: equipment.position
                        });
                        
                        if (response.data.code === 200) {
                            this.loadEquipments();
                            alert('装备穿戴成功');
                        } else {
                            alert(response.data.message || '穿戴失败');
                        }
                    } catch (error) {
                        console.error('穿戴装备失败：', error);
                        alert('穿戴失败，请重试');
                    }
                },
                
                // 卸下装备
                async unwearEquipment(equipment) {
                    if (!confirm(`确定要卸下装备"${equipment.name}"吗？`)) {
                        return;
                    }
                    
                    try {
                        const response = await axios.post('/UserEquipment/Unwear', null, {
                            params: { equipmentId: equipment.id }
                        });
                        
                        if (response.data.code === 200) {
                            this.loadEquipments();
                            alert('装备卸下成功');
                        } else {
                            alert(response.data.message || '卸下失败');
                        }
                    } catch (error) {
                        console.error('卸下装备失败：', error);
                        alert('卸下失败，请重试');
                    }
                },
                
                // 获取强化等级徽章样式
                getStrengthenBadgeClass(level) {
                    if (level >= 15) return 'cyber-badge cyber-badge-legendary';
                    if (level >= 10) return 'cyber-badge cyber-badge-epic';
                    if (level >= 5) return 'cyber-badge cyber-badge-rare';
                    return 'cyber-badge cyber-badge-common';
                },
                
                // 装备配置选择变化时
                onEquipmentConfigChange() {
                    if (!this.equipmentForm.equipId) return;
                    
                    // 从装备配置选项中找到选中的配置
                    const selectedConfig = this.equipmentConfigOptions.find(option => option.value === this.equipmentForm.equipId);
                    if (selectedConfig) {
                        // 自动填充装备信息
                        this.equipmentForm.name = selectedConfig.name;
                        this.equipmentForm.icon = selectedConfig.icon;
                        this.equipmentForm.equipTypeId = selectedConfig.equipTypeId;
                    }
                },
                
                // 格式化日期时间
                formatDateTime(dateStr) {
                    if (!dateStr) return '';
                    const date = new Date(dateStr);
                    return date.toLocaleString('zh-CN');
                },
                
                // 加载下拉选项
                async loadOptions() {
                    try {
                        // 加载用户选项
                        const userResponse = await axios.get('/UserEquipment/GetUserOptions');
                        if (userResponse.data.code === 200) {
                            this.userOptions = userResponse.data.data;
                            // 更新全局变量
                            userOptions = userResponse.data.data;
                            // 更新用户下拉框
                            updateUserSelect();
                        }

                        // 加载装备类型选项
                        const typeResponse = await axios.get('/UserEquipment/GetEquipmentTypeOptions');
                        if (typeResponse.data.code === 200) {
                            this.equipmentTypeOptions = typeResponse.data.data;
                        }

                        // 加载装备配置选项
                        const configResponse = await axios.get('/UserEquipment/GetEquipmentConfigOptions');
                        if (configResponse.data.code === 200) {
                            this.equipmentConfigOptions = configResponse.data.data;
                            this.allEquipments = configResponse.data.data; // 同时存储到allEquipments用于搜索
                            this.filteredEquipments = configResponse.data.data; // 初始化过滤列表

                            // 更新全局变量
                            allEquipments = configResponse.data.data;
                            filteredEquipments = configResponse.data.data;
                        }
                    } catch (error) {
                        console.error('加载选项失败：', error);
                    }
                },
                
                // 关闭装备模态框
                closeModal() {
                    // 重置装备搜索相关状态
                    this.showEquipmentDropdown = false;
                    this.equipmentSearchText = '';

                    $('#equipmentModal').modal('hide');
                },
                
                // 关闭装备详情模态框
                closeDetailModal() {
                    $('#equipmentDetailModal').modal('hide');
                },
                
                // 关闭强化模态框
                closeStrengthenModal() {
                    $('#strengthenModal').modal('hide');
                },

                // 装备搜索下拉框相关方法
                toggleEquipmentDropdown() {
                    this.showEquipmentDropdown = !this.showEquipmentDropdown;
                    if (this.showEquipmentDropdown) {
                        this.filteredEquipments = this.allEquipments; // 显示所有装备
                    }
                },

                filterEquipments() {
                    const searchText = this.equipmentSearchText.toLowerCase();
                    this.filteredEquipments = this.allEquipments.filter(equipment =>
                        equipment.label.toLowerCase().includes(searchText) ||
                        equipment.value.toString().includes(searchText) ||
                        (equipment.name && equipment.name.toLowerCase().includes(searchText))
                    );
                },

                selectEquipment(equipment) {
                    this.equipmentForm.equipId = equipment.value;
                    this.showEquipmentDropdown = false;
                    this.equipmentSearchText = '';
                    // 触发装备配置变化事件
                    this.onEquipmentConfigChange();
                },

                // 获取装备类型名称
                getEquipmentTypeName(equipTypeId) {
                    const type = this.equipmentTypeOptions.find(t => t.value === equipTypeId);
                    return type ? type.label : '未知类型';
                },

                // 处理点击外部关闭下拉框
                handleOutsideClick(event) {
                    const container = event.target.closest('.custom-select-container');
                    if (!container) {
                        this.showEquipmentDropdown = false;
                    }
                }
            },
            
            mounted() {
                // 初始化加载
                this.loadOptions();
                this.loadEquipments();

                // 添加点击外部关闭下拉框的事件监听器
                document.addEventListener('click', this.handleOutsideClick);
            }
        });

        // 挂载Vue应用
        try {
            app.mount('#userEquipmentApp');
            console.log('用户装备管理应用挂载成功');
        } catch (error) {
            console.error('Vue应用挂载失败:', error);
            document.getElementById('userEquipmentApp').innerHTML = '<div class="alert alert-danger">应用初始化失败: ' + error.message + '</div>';
        }

        } // 结束Vue和axios检查
    </script>
}